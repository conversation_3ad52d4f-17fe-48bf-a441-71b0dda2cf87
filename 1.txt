unsigned __int64 __fastcall UpdateDaemon::Slot_ReadClient(UpdateDaemon *this)
{
  __int64 v3; // rbx
  signed __int32 v4; // et0
  int v5; // edx
  QTextStream *v6; // rbx
  bool v7; // zf
  char *v8; // r14
  __int64 v9; // rax
  QTextStream *v10; // rax
  _BYTE *v11; // rdx
  unsigned __int64 v12; // rsi
  QString *v13; // rdi
  unsigned int v14; // eax
  char *v15; // r15
  __int64 v16; // rbx
  __int64 v17; // rax
  __int64 v18; // rax
  int v19; // edx
  QTextStream *v20; // rbx
  __int64 v21; // rax
  __int64 v22; // rax
  QTextStream *v23; // rax
  int *v24; // rdi
  int v25; // edx
  int v26; // ebx
  const char *v27; // rsi
  int v28; // eax
  int v29; // edx
  _BYTE *v30; // rcx
  __int64 v31; // rbp
  __int64 v32; // rax
  __int64 v33; // rax
  QTextStream *v34; // rdi
  __int64 v35; // rax
  __int64 v36; // rax
  QTextStream *v37; // rbp
  int v38; // edx
  __int64 v39; // rax
  int v40; // edx
  signed __int32 v41; // et0
  __int64 v42; // rax
  QTextStream *v43; // rax
  int *v44; // rdi
  QtServiceBase *v45; // rdi
  __int64 v46; // rax
  int v47; // edx
  __int64 v48; // rax
  int v49; // edx
  signed __int32 v50; // et0
  __int64 v51; // rax
  QTextStream *v52; // rax
  __int64 v53; // rbp
  int v54; // r12d
  __int64 v55; // rbx
  __int64 v56; // rax
  QTextStream *v57; // rax
  __int64 v58; // rcx
  __int64 v59; // rbx
  __int64 v60; // rax
  __int64 v61; // r13
  __int64 v62; // rbx
  __int64 v63; // rax
  __int64 v64; // rax
  QTextStream *v65; // rbx
  char *v66; // rsi
  char *v67; // rsi
  signed __int32 v68; // et0
  char *v69; // rsi
  signed __int32 v70; // et0
  __int64 v71; // rax
  int v72; // edx
  signed __int32 v73; // et0
  __int64 v74; // rax
  QTextStream *v75; // rax
  int v76; // edx
  signed __int32 v77; // et0
  signed __int32 v78; // et0
  int v79; // edx
  int v80; // r12d
  __int64 v81; // rbp
  __int64 v82; // rax
  __int64 v83; // rax
  QTextStream *v84; // rbp
  QTextStream *v85; // rax
  volatile signed __int32 *v86; // rdx
  __int64 v87; // rax
  __int64 v88; // rbx
  __int64 v89; // rbp
  __int64 v90; // rax
  QTextStream *v91; // rax
  char started; // bl
  signed __int32 v93; // et0
  int v94; // edx
  int v95; // edx
  __int64 v96; // rbp
  __int64 v97; // rax
  __int64 v98; // rax
  int v99; // edx
  QTextStream *v100; // rbp
  signed __int32 v101; // et0
  int v102; // edx
  int v103; // edx
  __int64 v104; // rax
  QTextStream *v105; // rax
  int v106; // edx
  __int64 v107; // rbp
  __int64 v108; // rax
  __int64 v109; // rax
  QTextStream *v110; // rbp
  int v111; // edx
  int v112; // edx
  __int64 v113; // rax
  QTextStream *v114; // rax
  char *v115; // rsi
  _QWORD *v116; // rax
  QTextStream *v117; // rax
  _QWORD *v118; // rax
  QAbstractSocket *v119; // [rsp+8h] [rbp-6A0h]
  int v120[4]; // [rsp+10h] [rbp-698h] BYREF
  int v121[4]; // [rsp+20h] [rbp-688h] BYREF
  int v122[4]; // [rsp+30h] [rbp-678h] BYREF
  int v123[4]; // [rsp+40h] [rbp-668h] BYREF
  int v124[4]; // [rsp+50h] [rbp-658h] BYREF
  int v125[4]; // [rsp+60h] [rbp-648h] BYREF
  void *v126[2]; // [rsp+70h] [rbp-638h] BYREF
  void *v127; // [rsp+80h] [rbp-628h] BYREF
  volatile signed __int32 *v128; // [rsp+90h] [rbp-618h] BYREF
  QTextStream *v129; // [rsp+A0h] [rbp-608h]
  _BYTE v130[16]; // [rsp+B0h] [rbp-5F8h] BYREF
  volatile signed __int32 *v131; // [rsp+C0h] [rbp-5E8h]
  volatile signed __int32 *v132[2]; // [rsp+D0h] [rbp-5D8h] BYREF
  int v133[2]; // [rsp+E0h] [rbp-5C8h] BYREF
  void *v134; // [rsp+F0h] [rbp-5B8h]
  QTextStream *v135; // [rsp+100h] [rbp-5A8h]
  _BYTE v136[16]; // [rsp+110h] [rbp-598h] BYREF
  volatile signed __int32 *v137; // [rsp+120h] [rbp-588h]
  volatile signed __int32 *v138[2]; // [rsp+130h] [rbp-578h] BYREF
  volatile signed __int32 *v139; // [rsp+140h] [rbp-568h] BYREF
  volatile signed __int32 *v140; // [rsp+150h] [rbp-558h] BYREF
  volatile signed __int32 *v141; // [rsp+160h] [rbp-548h] BYREF
  volatile signed __int32 *v142; // [rsp+170h] [rbp-538h] BYREF
  volatile signed __int32 *v143; // [rsp+180h] [rbp-528h] BYREF
  volatile signed __int32 *v144; // [rsp+190h] [rbp-518h] BYREF
  volatile signed __int32 *v145[2]; // [rsp+1A0h] [rbp-508h] BYREF
  volatile signed __int32 *v146[2]; // [rsp+1B0h] [rbp-4F8h] BYREF
  volatile signed __int32 *v147; // [rsp+1C0h] [rbp-4E8h] BYREF
  void *v148[2]; // [rsp+1D0h] [rbp-4D8h] BYREF
  volatile signed __int32 *v149; // [rsp+1E0h] [rbp-4C8h] BYREF
  void *v150[2]; // [rsp+1F0h] [rbp-4B8h] BYREF
  void *v151[2]; // [rsp+200h] [rbp-4A8h] BYREF
  volatile signed __int32 *v152[2]; // [rsp+210h] [rbp-498h] BYREF
  void *v153; // [rsp+220h] [rbp-488h] BYREF
  QTextStream *v154; // [rsp+230h] [rbp-478h]
  volatile signed __int32 *v155; // [rsp+240h] [rbp-468h] BYREF
  void *v156; // [rsp+250h] [rbp-458h]
  _BYTE v157[16]; // [rsp+260h] [rbp-448h] BYREF
  volatile signed __int32 *v158; // [rsp+270h] [rbp-438h]
  volatile signed __int32 *v159[2]; // [rsp+280h] [rbp-428h] BYREF
  volatile signed __int32 *v160; // [rsp+290h] [rbp-418h] BYREF
  volatile signed __int32 *v161; // [rsp+2A0h] [rbp-408h] BYREF
  volatile signed __int32 *v162; // [rsp+2B0h] [rbp-3F8h] BYREF
  volatile signed __int32 *v163; // [rsp+2C0h] [rbp-3E8h] BYREF
  volatile signed __int32 *v164; // [rsp+2D0h] [rbp-3D8h] BYREF
  volatile signed __int32 *v165; // [rsp+2E0h] [rbp-3C8h] BYREF
  QTextStream *v166; // [rsp+2F0h] [rbp-3B8h]
  volatile signed __int32 *v167[2]; // [rsp+300h] [rbp-3A8h] BYREF
  volatile signed __int32 *v168[2]; // [rsp+310h] [rbp-398h] BYREF
  volatile signed __int32 *v169[2]; // [rsp+320h] [rbp-388h] BYREF
  volatile signed __int32 *v170[2]; // [rsp+330h] [rbp-378h] BYREF
  _BYTE v171[16]; // [rsp+340h] [rbp-368h] BYREF
  volatile signed __int32 *v172; // [rsp+350h] [rbp-358h]
  volatile signed __int32 *v173[2]; // [rsp+360h] [rbp-348h] BYREF
  volatile signed __int32 *v174; // [rsp+370h] [rbp-338h] BYREF
  QTextStream *v175; // [rsp+380h] [rbp-328h]
  volatile signed __int32 *v176; // [rsp+390h] [rbp-318h] BYREF
  int v177[2]; // [rsp+3A0h] [rbp-308h] BYREF
  __int64 v178[2]; // [rsp+3B0h] [rbp-2F8h] BYREF
  volatile signed __int32 *v179[2]; // [rsp+3C0h] [rbp-2E8h] BYREF
  _BYTE v180[16]; // [rsp+3D0h] [rbp-2D8h] BYREF
  volatile signed __int32 *v181; // [rsp+3E0h] [rbp-2C8h]
  volatile signed __int32 *v182[2]; // [rsp+3F0h] [rbp-2B8h] BYREF
  volatile signed __int32 *v183; // [rsp+400h] [rbp-2A8h] BYREF
  int v184[2]; // [rsp+410h] [rbp-298h] BYREF
  __int64 v185[2]; // [rsp+420h] [rbp-288h] BYREF
  volatile signed __int32 *v186[2]; // [rsp+430h] [rbp-278h] BYREF
  QTextStream *v187; // [rsp+440h] [rbp-268h]
  volatile signed __int32 *v188; // [rsp+450h] [rbp-258h] BYREF
  int v189[2]; // [rsp+460h] [rbp-248h] BYREF
  __int64 v190[2]; // [rsp+470h] [rbp-238h] BYREF
  volatile signed __int32 *v191[2]; // [rsp+480h] [rbp-228h] BYREF
  _BYTE v192[16]; // [rsp+490h] [rbp-218h] BYREF
  volatile signed __int32 *v193; // [rsp+4A0h] [rbp-208h]
  volatile signed __int32 *v194[2]; // [rsp+4B0h] [rbp-1F8h] BYREF
  volatile signed __int32 *v195; // [rsp+4C0h] [rbp-1E8h] BYREF
  int v196[2]; // [rsp+4D0h] [rbp-1D8h] BYREF
  __int64 v197[2]; // [rsp+4E0h] [rbp-1C8h] BYREF
  volatile signed __int32 *v198[2]; // [rsp+4F0h] [rbp-1B8h] BYREF
  volatile signed __int32 *v199; // [rsp+500h] [rbp-1A8h] BYREF
  void *v200; // [rsp+510h] [rbp-198h]
  _BYTE v201[16]; // [rsp+520h] [rbp-188h] BYREF
  volatile signed __int32 *v202; // [rsp+530h] [rbp-178h]
  volatile signed __int32 *v203[2]; // [rsp+540h] [rbp-168h] BYREF
  QtServiceBase *v204; // [rsp+550h] [rbp-158h]
  void *v205; // [rsp+560h] [rbp-148h]
  volatile signed __int32 *v206; // [rsp+570h] [rbp-138h] BYREF
  void *v207; // [rsp+580h] [rbp-128h]
  _BYTE v208[16]; // [rsp+590h] [rbp-118h] BYREF
  volatile signed __int32 *v209; // [rsp+5A0h] [rbp-108h]
  volatile signed __int32 *v210[2]; // [rsp+5B0h] [rbp-F8h] BYREF
  void *v211[2]; // [rsp+5C0h] [rbp-E8h] BYREF
  void *v212[2]; // [rsp+5D0h] [rbp-D8h] BYREF
  void *v213[2]; // [rsp+5E0h] [rbp-C8h] BYREF
  void *v214[2]; // [rsp+5F0h] [rbp-B8h] BYREF
  volatile signed __int32 *v215[2]; // [rsp+600h] [rbp-A8h] BYREF
  void *v216[2]; // [rsp+610h] [rbp-98h] BYREF
  volatile signed __int32 *v217[2]; // [rsp+620h] [rbp-88h] BYREF
  int v218[2]; // [rsp+630h] [rbp-78h] BYREF
  void *v219[2]; // [rsp+640h] [rbp-68h] BYREF
  QTextStream *v220[2]; // [rsp+650h] [rbp-58h] BYREF
  void *v221; // [rsp+660h] [rbp-48h] BYREF
  unsigned __int64 v222; // [rsp+668h] [rbp-40h]

  v222 = __readfsqword(0x28u);
  if ( *((_BYTE *)this + 24) )
    return __readfsqword(0x28u) ^ v222;
  v119 = (QAbstractSocket *)QObject::sender(this);
  v3 = operator new(0x28u);
  LODWORD(v221) = 2;
  QTextStream::QTextStream(v3, v3 + 16, &v221);
  *(_QWORD *)(v3 + 16) = &QString::shared_null;
  _InterlockedIncrement(&QString::shared_null);
  *(_DWORD *)(v3 + 24) = 1;
  *(_DWORD *)(v3 + 28) = 0;
  *(_BYTE *)(v3 + 32) = 1;
  *(_BYTE *)(v3 + 33) = 1;
  v220[0] = (QTextStream *)v3;
  QString::fromAscii((QString *)&v221, "read client message.", -1);
  QTextStream::operator<<(v3, &v221);
  v4 = _InterlockedDecrement((volatile signed __int32 *)v221);
  LOBYTE(v5) = v4 != 0;
  if ( !v4 )
    QString::free(v221);
  v6 = v220[0];
  if ( *((_BYTE *)v220[0] + 32) )
  {
    QTextStream::operator<<(v220[0], 32);
    v6 = v220[0];
    v7 = (*((_DWORD *)v220[0] + 6))-- == 1;
    if ( !v7 )
      goto LABEL_11;
  }
  else
  {
    v7 = (*((_DWORD *)v220[0] + 6))-- == 1;
    if ( !v7 )
      goto LABEL_11;
  }
  if ( !*((_BYTE *)v6 + 33) )
    goto LABEL_304;
  QString::toLocal8Bit((QString *)v211);
  if ( *(_DWORD *)v211[0] != 1 || (v67 = (char *)*((_QWORD *)v211[0] + 2), v67 != (char *)v211[0] + 24) )
  {
    QByteArray::realloc((QByteArray *)v211, *((_DWORD *)v211[0] + 2));
    v67 = (char *)*((_QWORD *)v211[0] + 2);
  }
  qt_message_output(*((unsigned int *)v220[0] + 7), v67);
  v68 = _InterlockedDecrement((volatile signed __int32 *)v211[0]);
  LOBYTE(v5) = v68 != 0;
  if ( !v68 )
    qFree(v211[0]);
  v6 = v220[0];
  if ( v220[0] )
  {
LABEL_304:
    if ( !_InterlockedDecrement(*((volatile signed __int32 **)v6 + 2)) )
      QString::free(*((_QWORD *)v6 + 2));
    QTextStream::~QTextStream(v6);
    operator delete(v6);
  }
LABEL_11:
  v220[0] = (QTextStream *)QString::fromAscii_helper(
                             (QString *)"yyyy.MM.dd hh:mm:ss.zzz ",
                             (const char *)0xFFFFFFFFLL,
                             v5);
  QDateTime::currentDateTime((QDateTime *)v219);
  QDateTime::toString((QDateTime *)&v221, (const QString *)v219);
  v8 = (char *)this + 32;
  v9 = QTextStream::operator<<((char *)this + 32, &v221);
  v10 = (QTextStream *)QTextStream::operator<<(v9, "read client message.");
  endl(v10);
  if ( !_InterlockedDecrement((volatile signed __int32 *)v221) )
    QString::free(v221);
  QDateTime::~QDateTime((QDateTime *)v219);
  if ( !_InterlockedDecrement((volatile signed __int32 *)v220[0]) )
    QString::free(v220[0]);
  QIODevice::readAll((QIODevice *)v126);
  QByteArray::fromBase64((QByteArray *)&v127, (const QByteArray *)v126);
  LODWORD(v11) = (_DWORD)v127;
  v12 = 0;
  v13 = (QString *)*((_QWORD *)v127 + 2);
  v14 = *((_DWORD *)v127 + 2);
  if ( v13 && v14 && *(_BYTE *)v13 )
  {
    v11 = (char *)v13 + 1;
    LODWORD(v12) = 0;
    while ( 1 )
    {
      v12 = (unsigned int)(v12 + 1);
      if ( v14 == (_DWORD)v12 )
        break;
      if ( !*v11++ )
        goto LABEL_21;
    }
    v12 = v14;
  }
LABEL_21:
  v15 = (char *)this + 16;
  v128 = (volatile signed __int32 *)QString::fromAscii_helper(v13, (const char *)v12, (int)v11);
  QString::operator=(v15, &v128);
  if ( !_InterlockedDecrement(v128) )
    QString::free(v128);
  if ( !_InterlockedDecrement((volatile signed __int32 *)v127) )
    qFree(v127);
  v16 = operator new(0x28u);
  v120[0] = 2;
  QTextStream::QTextStream(v16, v16 + 16, v120);
  *(_QWORD *)(v16 + 16) = &QString::shared_null;
  _InterlockedIncrement(&QString::shared_null);
  *(_DWORD *)(v16 + 24) = 1;
  *(_DWORD *)(v16 + 28) = 0;
  *(_BYTE *)(v16 + 32) = 1;
  *(_BYTE *)(v16 + 33) = 1;
  v129 = (QTextStream *)v16;
  v17 = QTextStream::operator<<(v16, 34);
  v18 = QTextStream::operator<<(v17, v15);
  QTextStream::operator<<(v18, 34);
  v20 = v129;
  if ( *((_BYTE *)v129 + 32) )
  {
    QTextStream::operator<<(v129, 32);
    v20 = v129;
    v7 = (*((_DWORD *)v129 + 6))-- == 1;
    if ( !v7 )
      goto LABEL_31;
  }
  else
  {
    v7 = (*((_DWORD *)v129 + 6))-- == 1;
    if ( !v7 )
      goto LABEL_31;
  }
  if ( !*((_BYTE *)v20 + 33) )
    goto LABEL_305;
  QString::toLocal8Bit((QString *)v212);
  if ( *(_DWORD *)v212[0] != 1 || (v69 = (char *)*((_QWORD *)v212[0] + 2), v69 != (char *)v212[0] + 24) )
  {
    QByteArray::realloc((QByteArray *)v212, *((_DWORD *)v212[0] + 2));
    v69 = (char *)*((_QWORD *)v212[0] + 2);
  }
  qt_message_output(*((unsigned int *)v129 + 7), v69);
  v70 = _InterlockedDecrement((volatile signed __int32 *)v212[0]);
  LOBYTE(v19) = v70 != 0;
  if ( !v70 )
    qFree(v212[0]);
  v20 = v129;
  if ( v129 )
  {
LABEL_305:
    if ( !_InterlockedDecrement(*((volatile signed __int32 **)v20 + 2)) )
      QString::free(*((_QWORD *)v20 + 2));
    QTextStream::~QTextStream(v20);
    operator delete(v20);
  }
LABEL_31:
  v131 = (volatile signed __int32 *)QString::fromAscii_helper(
                                      (QString *)"yyyy.MM.dd hh:mm:ss.zzz ",
                                      (const char *)0xFFFFFFFFLL,
                                      v19);
  QDateTime::currentDateTime((QDateTime *)v130);
  QDateTime::toString((QDateTime *)v132, (const QString *)v130);
  v21 = QTextStream::operator<<(v8, v132);
  v22 = QTextStream::operator<<(v21, "receive message is : ");
  v23 = (QTextStream *)QTextStream::operator<<(v22, v15);
  endl(v23);
  if ( !_InterlockedDecrement(v132[0]) )
    QString::free(v132[0]);
  QDateTime::~QDateTime((QDateTime *)v130);
  if ( !_InterlockedDecrement(v131) )
    QString::free(v131);
  v24 = v133;
  QString::split(v133, v15, &g_UpdateDataSplit, 0, 1);
  if ( *(_DWORD *)(*(_QWORD *)v133 + 12LL) - *(_DWORD *)(*(_QWORD *)v133 + 8LL) == 7 )
  {
    v53 = 0;
    v54 = 0;
    do
    {
      v134 = &QByteArray::shared_null;
      _InterlockedIncrement(&QByteArray::shared_null);
      v59 = *(_QWORD *)v133 + 8 * (v53 + *(int *)(*(_QWORD *)v133 + 8LL)) + 24;
      v60 = QtServiceBase::instance((QtServiceBase *)v24);
      QtServiceBase::logMessage(v60, v59, 0);
      if ( !_InterlockedDecrement((volatile signed __int32 *)v134) )
        qFree(v134);
      v61 = *(_QWORD *)v133 + 8 * (v53 + *(int *)(*(_QWORD *)v133 + 8LL)) + 24;
      v62 = operator new(0x28u);
      v121[0] = 2;
      QTextStream::QTextStream(v62, v62 + 16, v121);
      *(_QWORD *)(v62 + 16) = &QString::shared_null;
      _InterlockedIncrement(&QString::shared_null);
      *(_DWORD *)(v62 + 24) = 1;
      *(_DWORD *)(v62 + 28) = 0;
      *(_BYTE *)(v62 + 32) = 1;
      *(_BYTE *)(v62 + 33) = 1;
      v135 = (QTextStream *)v62;
      v63 = QTextStream::operator<<(v62, 34);
      v64 = QTextStream::operator<<(v63, v61);
      QTextStream::operator<<(v64, 34);
      v65 = v135;
      if ( *((_BYTE *)v135 + 32) )
      {
        QTextStream::operator<<(v135, 32);
        v65 = v135;
      }
      v7 = (*((_DWORD *)v65 + 6))-- == 1;
      if ( v7 )
      {
        if ( !*((_BYTE *)v65 + 33) )
          goto LABEL_306;
        QString::toLocal8Bit((QString *)v213);
        if ( *(_DWORD *)v213[0] != 1 || (v66 = (char *)*((_QWORD *)v213[0] + 2), v66 != (char *)v213[0] + 24) )
        {
          QByteArray::realloc((QByteArray *)v213, *((_DWORD *)v213[0] + 2));
          v66 = (char *)*((_QWORD *)v213[0] + 2);
        }
        qt_message_output(*((unsigned int *)v135 + 7), v66);
        if ( !_InterlockedDecrement((volatile signed __int32 *)v213[0]) )
          qFree(v213[0]);
        v65 = v135;
        if ( v135 )
        {
LABEL_306:
          if ( !_InterlockedDecrement(*((volatile signed __int32 **)v65 + 2)) )
            QString::free(*((_QWORD *)v65 + 2));
          QTextStream::~QTextStream(v65);
          operator delete(v65);
        }
      }
      v55 = *(_QWORD *)v133 + 8 * (v53 + *(int *)(*(_QWORD *)v133 + 8LL)) + 24;
      v137 = (volatile signed __int32 *)QString::fromAscii_helper(
                                          (QString *)"yyyy.MM.dd hh:mm:ss.zzz ",
                                          (const char *)0xFFFFFFFFLL,
                                          v133[0]);
      QDateTime::currentDateTime((QDateTime *)v136);
      QDateTime::toString((QDateTime *)v138, (const QString *)v136);
      v56 = QTextStream::operator<<(v8, v138);
      v57 = (QTextStream *)QTextStream::operator<<(v56, v55);
      endl(v57);
      if ( !_InterlockedDecrement(v138[0]) )
        QString::free(v138[0]);
      v24 = (int *)v136;
      QDateTime::~QDateTime((QDateTime *)v136);
      if ( !_InterlockedDecrement(v137) )
      {
        v24 = (int *)v137;
        QString::free(v137);
      }
      ++v54;
      ++v53;
      v58 = *(int *)(*(_QWORD *)v133 + 8LL);
    }
    while ( v54 < *(_DWORD *)(*(_QWORD *)v133 + 12LL) - (int)v58 );
    v139 = *(volatile signed __int32 **)(*(_QWORD *)v133 + 8 * v58 + 24);
    _InterlockedIncrement(v139);
    v140 = *(volatile signed __int32 **)(*(_QWORD *)v133 + 8LL * *(int *)(*(_QWORD *)v133 + 8LL) + 32);
    _InterlockedIncrement(v140);
    v141 = *(volatile signed __int32 **)(*(_QWORD *)v133 + 8LL * *(int *)(*(_QWORD *)v133 + 8LL) + 40);
    _InterlockedIncrement(v141);
    v142 = *(volatile signed __int32 **)(*(_QWORD *)v133 + 8LL * *(int *)(*(_QWORD *)v133 + 8LL) + 48);
    _InterlockedIncrement(v142);
    v143 = *(volatile signed __int32 **)(*(_QWORD *)v133 + 8LL * *(int *)(*(_QWORD *)v133 + 8LL) + 56);
    _InterlockedIncrement(v143);
    v144 = *(volatile signed __int32 **)(*(_QWORD *)v133 + 8LL * *(int *)(*(_QWORD *)v133 + 8LL) + 64);
    _InterlockedIncrement(v144);
    v145[0] = *(volatile signed __int32 **)(*(_QWORD *)v133 + 8LL * *(int *)(*(_QWORD *)v133 + 8LL) + 72);
    _InterlockedIncrement(v145[0]);
    QString::lastIndexOf(v15, &g_UpdateDataSplit, 0xFFFFFFFFLL, 1);
    QString::left((QString *)v146, (int)v15);
    v147 = (volatile signed __int32 *)QString::fromAscii_helper(
                                        (QString *)"suwellservice",
                                        (const char *)0xFFFFFFFFLL,
                                        v25);
    v149 = v146[0];
    _InterlockedIncrement(v146[0]);
    QString::append((QString *)&v149, (const QString *)&v147);
    QString::toUtf8((QString *)v150);
    QCryptographicHash::hash(v148, v150, 1);
    if ( !_InterlockedDecrement((volatile signed __int32 *)v150[0]) )
      qFree(v150[0]);
    if ( !_InterlockedDecrement(v149) )
      QString::free(v149);
    v26 = 4;
    do
    {
      QString::toAscii((QString *)v214);
      QByteArray::append((QByteArray *)v148, (const QByteArray *)v214);
      if ( !_InterlockedDecrement((volatile signed __int32 *)v214[0]) )
        qFree(v214[0]);
      QCryptographicHash::hash(v151, v148, 1);
      QByteArray::operator=(v148, v151);
      if ( !_InterlockedDecrement((volatile signed __int32 *)v151[0]) )
        qFree(v151[0]);
      --v26;
    }
    while ( v26 );
    v152[0] = &QString::shared_null;
    _InterlockedIncrement(&QString::shared_null);
    QByteArray::toHex((QByteArray *)&v153);
    v27 = (const char *)*((_QWORD *)v153 + 2);
    v28 = *((_DWORD *)v153 + 2);
    v29 = 0;
    if ( v27 && v28 && *v27 )
    {
      v30 = v27 + 1;
      v29 = 0;
      while ( v28 != ++v29 )
      {
        if ( !*v30++ )
          goto LABEL_59;
      }
      v29 = *((_DWORD *)v153 + 2);
    }
LABEL_59:
    QString::fromAscii((QString *)v215, v27, v29);
    QString::append((QString *)v152, (const QString *)v215);
    if ( !_InterlockedDecrement(v215[0]) )
      QString::free(v215[0]);
    if ( !_InterlockedDecrement((volatile signed __int32 *)v153) )
      qFree(v153);
    v31 = operator new(0x28u);
    v122[0] = 2;
    QTextStream::QTextStream(v31, v31 + 16, v122);
    *(_QWORD *)(v31 + 16) = &QString::shared_null;
    _InterlockedIncrement(&QString::shared_null);
    *(_DWORD *)(v31 + 24) = 1;
    *(_DWORD *)(v31 + 28) = 0;
    *(_BYTE *)(v31 + 32) = 1;
    *(_BYTE *)(v31 + 33) = 1;
    v154 = (QTextStream *)v31;
    v32 = QTextStream::operator<<(v31, 34);
    v33 = QTextStream::operator<<(v32, v145);
    QTextStream::operator<<(v33, 34);
    v34 = v154;
    if ( *((_BYTE *)v154 + 32) )
    {
      QTextStream::operator<<(v154, 32);
      v34 = v154;
    }
    v35 = QTextStream::operator<<(v34, 34);
    v36 = QTextStream::operator<<(v35, v152);
    QTextStream::operator<<(v36, 34);
    v37 = v154;
    if ( *((_BYTE *)v154 + 32) )
    {
      QTextStream::operator<<(v154, 32);
      v37 = v154;
    }
    v7 = (*((_DWORD *)v37 + 6))-- == 1;
    if ( v7 )
    {
      if ( !*((_BYTE *)v37 + 33) )
        goto LABEL_307;
      QString::toLocal8Bit((QString *)v216);
      if ( *(_DWORD *)v216[0] != 1 || (v115 = (char *)*((_QWORD *)v216[0] + 2), v115 != (char *)v216[0] + 24) )
      {
        QByteArray::realloc((QByteArray *)v216, *((_DWORD *)v216[0] + 2));
        v115 = (char *)*((_QWORD *)v216[0] + 2);
      }
      qt_message_output(*((unsigned int *)v154 + 7), v115);
      if ( !_InterlockedDecrement((volatile signed __int32 *)v216[0]) )
        qFree(v216[0]);
      v37 = v154;
      if ( v154 )
      {
LABEL_307:
        if ( !_InterlockedDecrement(*((volatile signed __int32 **)v37 + 2)) )
          QString::free(*((_QWORD *)v37 + 2));
        QTextStream::~QTextStream(v37);
        operator delete(v37);
      }
    }
    if ( (unsigned __int8)QString::operator==(v145, v152) )
    {
      v156 = &QByteArray::shared_null;
      _InterlockedIncrement(&QByteArray::shared_null);
      v155 = (volatile signed __int32 *)QString::fromAscii_helper(
                                          (QString *)"check md5 success.",
                                          (const char *)0xFFFFFFFFLL,
                                          v38);
      v71 = QtServiceBase::instance((QtServiceBase *)"check md5 success.");
      QtServiceBase::logMessage(v71, (__int64)&v155, 0);
      if ( !_InterlockedDecrement(v155) )
        QString::free(v155);
      v73 = _InterlockedDecrement((volatile signed __int32 *)v156);
      LOBYTE(v72) = v73 != 0;
      if ( !v73 )
        qFree(v156);
      v158 = (volatile signed __int32 *)QString::fromAscii_helper(
                                          (QString *)"yyyy.MM.dd hh:mm:ss.zzz ",
                                          (const char *)0xFFFFFFFFLL,
                                          v72);
      QDateTime::currentDateTime((QDateTime *)v157);
      QDateTime::toString((QDateTime *)v159, (const QString *)v157);
      v74 = QTextStream::operator<<(v8, v159);
      v75 = (QTextStream *)QTextStream::operator<<(v74, "check md5 success.");
      endl(v75);
      if ( !_InterlockedDecrement(v159[0]) )
        QString::free(v159[0]);
      QDateTime::~QDateTime((QDateTime *)v157);
      if ( !_InterlockedDecrement(v158) )
        QString::free(v158);
      QDir::tempPath((QDir *)&v160);
      v161 = v160;
      _InterlockedIncrement(v160);
      QString::fromAscii((QString *)v217, "/", -1);
      QString::append((QString *)&v161, (const QString *)v217);
      if ( !_InterlockedDecrement(v217[0]) )
        QString::free(v217[0]);
      v162 = v161;
      _InterlockedIncrement(v161);
      QString::append((QString *)&v162, (const QString *)&v141);
      v163 = v162;
      _InterlockedIncrement(v162);
      QString::fromAscii((QString *)v218, "/", -1);
      QString::append((QString *)&v163, (const QString *)v218);
      if ( !_InterlockedDecrement(*(volatile signed __int32 **)v218) )
        QString::free(*(_QWORD *)v218);
      v215[0] = v163;
      _InterlockedIncrement(v163);
      QString::append((QString *)v215, (const QString *)&v140);
      if ( !_InterlockedDecrement(v163) )
        QString::free(v163);
      if ( !_InterlockedDecrement(v162) )
        QString::free(v162);
      if ( !_InterlockedDecrement(v161) )
        QString::free(v161);
      v77 = _InterlockedDecrement(v160);
      LOBYTE(v76) = v77 != 0;
      if ( !v77 )
        QString::free(v160);
      v217[0] = &QListData::shared_null;
      _InterlockedIncrement(&QListData::shared_null);
      v164 = (volatile signed __int32 *)QString::fromAscii_helper((QString *)"777", (const char *)0xFFFFFFFFLL, v76);
      QList<QString>::append(v217, &v164);
      QList<QString>::append(v217, v215);
      v78 = _InterlockedDecrement(v164);
      LOBYTE(v79) = v78 != 0;
      if ( !v78 )
        QString::free(v164);
      v165 = (volatile signed __int32 *)QString::fromAscii_helper((QString *)"chmod", (const char *)0xFFFFFFFFLL, v79);
      v80 = QProcess::execute(&v165, v217);
      if ( !_InterlockedDecrement(v165) )
        QString::free(v165);
      QString::number((QString *)v167, v80, 10);
      QString::fromAscii((QString *)v168, "chmod suwellappupdate result:", -1);
      QString::append((QString *)v168, (const QString *)v167);
      v81 = operator new(0x28u);
      v123[0] = 2;
      QTextStream::QTextStream(v81, v81 + 16, v123);
      *(_QWORD *)(v81 + 16) = &QString::shared_null;
      _InterlockedIncrement(&QString::shared_null);
      *(_DWORD *)(v81 + 24) = 1;
      *(_DWORD *)(v81 + 28) = 0;
      *(_BYTE *)(v81 + 32) = 1;
      *(_BYTE *)(v81 + 33) = 1;
      v166 = (QTextStream *)v81;
      v82 = QTextStream::operator<<(v81, 34);
      v83 = QTextStream::operator<<(v82, v168);
      QTextStream::operator<<(v83, 34);
      v84 = v166;
      if ( *((_BYTE *)v166 + 32) )
      {
        QTextStream::operator<<(v166, 32);
        v84 = v166;
      }
      v7 = (*((_DWORD *)v84 + 6))-- == 1;
      if ( v7 )
      {
        if ( !*((_BYTE *)v84 + 33) )
          goto LABEL_308;
        QString::toLocal8Bit((QString *)v219);
        v116 = v219[0];
        if ( *(_DWORD *)v219[0] != 1 || *((void **)v219[0] + 2) != (char *)v219[0] + 24 )
        {
          QByteArray::realloc((QByteArray *)v219, *((_DWORD *)v219[0] + 2));
          v116 = v219[0];
        }
        qt_message_output(*((unsigned int *)v166 + 7), v116[2]);
        if ( !_InterlockedDecrement((volatile signed __int32 *)v219[0]) )
          qFree(v219[0]);
        v84 = v166;
        if ( v166 )
        {
LABEL_308:
          if ( !_InterlockedDecrement(*((volatile signed __int32 **)v84 + 2)) )
            QString::free(*((_QWORD *)v84 + 2));
          QTextStream::~QTextStream(v84);
          operator delete(v84);
        }
      }
      if ( !_InterlockedDecrement(v168[0]) )
        QString::free(v168[0]);
      if ( !_InterlockedDecrement(v167[0]) )
        QString::free(v167[0]);
      QString::number((QString *)v169, v80, 10);
      QString::fromAscii((QString *)v170, "chmod suwellappupdate result:", -1);
      QString::append((QString *)v170, (const QString *)v169);
      v85 = (QTextStream *)QTextStream::operator<<(v8, v170);
      endl(v85);
      if ( !_InterlockedDecrement(v170[0]) )
        QString::free(v170[0]);
      if ( !_InterlockedDecrement(v169[0]) )
        QString::free(v169[0]);
      *(_QWORD *)v218 = &QListData::shared_null;
      _InterlockedIncrement(&QListData::shared_null);
      QList<QString>::append(v218, v215);
      QList<QString>::append(v218, &v139);
      QList<QString>::append(v218, &v142);
      QList<QString>::append(v218, &v143);
      QList<QString>::append(v218, &v144);
      v86 = *(volatile signed __int32 **)v218;
      v87 = *(int *)(*(_QWORD *)v218 + 8LL);
      if ( *(_DWORD *)(*(_QWORD *)v218 + 12LL) - (int)v87 > 0 )
      {
        v88 = 0;
        do
        {
          v89 = (__int64)&v86[2 * v88 + 6 + 2 * v87];
          v172 = (volatile signed __int32 *)QString::fromAscii_helper(
                                              (QString *)"yyyy.MM.dd hh:mm:ss.zzz ",
                                              (const char *)0xFFFFFFFFLL,
                                              (int)v86);
          QDateTime::currentDateTime((QDateTime *)v171);
          QDateTime::toString((QDateTime *)v173, (const QString *)v171);
          v90 = QTextStream::operator<<(v8, v173);
          v91 = (QTextStream *)QTextStream::operator<<(v90, v89);
          endl(v91);
          if ( !_InterlockedDecrement(v173[0]) )
            QString::free(v173[0]);
          QDateTime::~QDateTime((QDateTime *)v171);
          if ( !_InterlockedDecrement(v172) )
            QString::free(v172);
          v86 = *(volatile signed __int32 **)v218;
          ++v88;
          v87 = *(int *)(*(_QWORD *)v218 + 8LL);
        }
        while ( *(_DWORD *)(*(_QWORD *)v218 + 12LL) - (int)v87 > (int)v88 );
      }
      v174 = (volatile signed __int32 *)QString::fromAscii_helper(
                                          (QString *)"bash",
                                          (const char *)0xFFFFFFFFLL,
                                          (int)v86);
      started = QProcess::startDetached(&v174, v218);
      v93 = _InterlockedDecrement(v174);
      LOBYTE(v94) = v93 != 0;
      if ( !v93 )
        QString::free(v174);
      if ( started )
      {
        LOWORD(v221) = 32;
        LOWORD(v214[0]) = 32;
        *(_QWORD *)v177 = QString::fromAscii_helper((QString *)"Slot_ReadClient", (const char *)0xFFFFFFFFLL, v94);
        v176 = (volatile signed __int32 *)QString::fromAscii_helper(
                                            (QString *)"[%1--%2] sh cmd success",
                                            (const char *)0xFFFFFFFFLL,
                                            v95);
        QString::arg((QString *)v178, (const QString *)&v176, (int)v177, 0);
        QString::arg((QString *)v179, (__int64)v178, 161, 0, (const QChar *)0xA);
        v96 = operator new(0x28u);
        v124[0] = 2;
        QTextStream::QTextStream(v96, v96 + 16, v124);
        *(_QWORD *)(v96 + 16) = &QString::shared_null;
        _InterlockedIncrement(&QString::shared_null);
        *(_DWORD *)(v96 + 24) = 1;
        *(_DWORD *)(v96 + 28) = 0;
        *(_BYTE *)(v96 + 32) = 1;
        *(_BYTE *)(v96 + 33) = 1;
        v175 = (QTextStream *)v96;
        v97 = QTextStream::operator<<(v96, 34);
        v98 = QTextStream::operator<<(v97, v179);
        QTextStream::operator<<(v98, 34);
        v100 = v175;
        if ( *((_BYTE *)v175 + 32) )
        {
          QTextStream::operator<<(v175, 32);
          v100 = v175;
        }
        v7 = (*((_DWORD *)v100 + 6))-- == 1;
        if ( v7 )
        {
          if ( !*((_BYTE *)v100 + 33) )
            goto LABEL_309;
          QString::toLocal8Bit((QString *)v220);
          v117 = v220[0];
          if ( *(_DWORD *)v220[0] != 1 || *((QTextStream **)v220[0] + 2) != (QTextStream *)((char *)v220[0] + 24) )
          {
            QByteArray::realloc((QByteArray *)v220, *((_DWORD *)v220[0] + 2));
            v117 = v220[0];
          }
          qt_message_output(*((unsigned int *)v175 + 7), *((_QWORD *)v117 + 2));
          if ( !_InterlockedDecrement((volatile signed __int32 *)v220[0]) )
            qFree(v220[0]);
          v100 = v175;
          if ( v175 )
          {
LABEL_309:
            if ( !_InterlockedDecrement(*((volatile signed __int32 **)v100 + 2)) )
              QString::free(*((_QWORD *)v100 + 2));
            QTextStream::~QTextStream(v100);
            operator delete(v100);
          }
        }
        if ( !_InterlockedDecrement(v179[0]) )
          QString::free(v179[0]);
        if ( !_InterlockedDecrement((volatile signed __int32 *)v178[0]) )
          QString::free(v178[0]);
        if ( !_InterlockedDecrement(v176) )
          QString::free(v176);
        v101 = _InterlockedDecrement(*(volatile signed __int32 **)v177);
        LOBYTE(v99) = v101 != 0;
        if ( !v101 )
          QString::free(*(_QWORD *)v177);
        LOWORD(v221) = 32;
        LOWORD(v214[0]) = 32;
        *(_QWORD *)v184 = QString::fromAscii_helper((QString *)"Slot_ReadClient", (const char *)0xFFFFFFFFLL, v99);
        v183 = (volatile signed __int32 *)QString::fromAscii_helper(
                                            (QString *)"[%1--%2] sh cmd success",
                                            (const char *)0xFFFFFFFFLL,
                                            v102);
        QString::arg((QString *)v185, (const QString *)&v183, (int)v184, 0);
        QString::arg((QString *)v186, (__int64)v185, 162, 0, (const QChar *)0xA);
        v181 = (volatile signed __int32 *)QString::fromAscii_helper(
                                            (QString *)"yyyy.MM.dd hh:mm:ss.zzz ",
                                            (const char *)0xFFFFFFFFLL,
                                            v103);
        QDateTime::currentDateTime((QDateTime *)v180);
        QDateTime::toString((QDateTime *)v182, (const QString *)v180);
        v104 = QTextStream::operator<<(v8, v182);
        v105 = (QTextStream *)QTextStream::operator<<(v104, v186);
        endl(v105);
        if ( !_InterlockedDecrement(v182[0]) )
          QString::free(v182[0]);
        v44 = (int *)v180;
        QDateTime::~QDateTime((QDateTime *)v180);
        if ( !_InterlockedDecrement(v181) )
        {
          v44 = (int *)v181;
          QString::free(v181);
        }
        if ( !_InterlockedDecrement(v186[0]) )
        {
          v44 = (int *)v186[0];
          QString::free(v186[0]);
        }
        if ( !_InterlockedDecrement((volatile signed __int32 *)v185[0]) )
        {
          v44 = (int *)v185[0];
          QString::free(v185[0]);
        }
        if ( !_InterlockedDecrement(v183) )
        {
          v44 = (int *)v183;
          QString::free(v183);
        }
        if ( !_InterlockedDecrement(*(volatile signed __int32 **)v184) )
        {
          v44 = *(int **)v184;
          QString::free(*(_QWORD *)v184);
        }
      }
      else
      {
        LOWORD(v214[0]) = 32;
        LOWORD(v220[0]) = 32;
        *(_QWORD *)v189 = QString::fromAscii_helper((QString *)"Slot_ReadClient", (const char *)0xFFFFFFFFLL, v94);
        v188 = (volatile signed __int32 *)QString::fromAscii_helper(
                                            (QString *)"[%1--%2] %3 cmd failed...failed...",
                                            (const char *)0xFFFFFFFFLL,
                                            v106);
        QString::arg((QString *)v190, (const QString *)&v188, (int)v189, 0);
        QString::arg((QString *)v191, (__int64)v190, 166, 0, (const QChar *)0xA);
        v107 = operator new(0x28u);
        v125[0] = 2;
        QTextStream::QTextStream(v107, v107 + 16, v125);
        *(_QWORD *)(v107 + 16) = &QString::shared_null;
        _InterlockedIncrement(&QString::shared_null);
        *(_DWORD *)(v107 + 24) = 1;
        *(_DWORD *)(v107 + 28) = 0;
        *(_BYTE *)(v107 + 32) = 1;
        *(_BYTE *)(v107 + 33) = 1;
        v187 = (QTextStream *)v107;
        v108 = QTextStream::operator<<(v107, 34);
        v109 = QTextStream::operator<<(v108, v191);
        QTextStream::operator<<(v109, 34);
        v110 = v187;
        if ( *((_BYTE *)v187 + 32) )
        {
          QTextStream::operator<<(v187, 32);
          v110 = v187;
        }
        v7 = (*((_DWORD *)v110 + 6))-- == 1;
        if ( v7 )
        {
          if ( !*((_BYTE *)v110 + 33) )
            goto LABEL_310;
          QString::toLocal8Bit((QString *)&v221);
          v118 = v221;
          if ( *(_DWORD *)v221 != 1 || *((void **)v221 + 2) != (char *)v221 + 24 )
          {
            QByteArray::realloc((QByteArray *)&v221, *((_DWORD *)v221 + 2));
            v118 = v221;
          }
          qt_message_output(*((unsigned int *)v187 + 7), v118[2]);
          if ( !_InterlockedDecrement((volatile signed __int32 *)v221) )
            qFree(v221);
          v110 = v187;
          if ( v187 )
          {
LABEL_310:
            if ( !_InterlockedDecrement(*((volatile signed __int32 **)v110 + 2)) )
              QString::free(*((_QWORD *)v110 + 2));
            QTextStream::~QTextStream(v110);
            operator delete(v110);
          }
        }
        if ( !_InterlockedDecrement(v191[0]) )
          QString::free(v191[0]);
        if ( !_InterlockedDecrement((volatile signed __int32 *)v190[0]) )
          QString::free(v190[0]);
        if ( !_InterlockedDecrement(v188) )
          QString::free(v188);
        if ( !_InterlockedDecrement(*(volatile signed __int32 **)v189) )
          QString::free(*(_QWORD *)v189);
        LOWORD(v220[0]) = 32;
        LOWORD(v214[0]) = 32;
        *(_QWORD *)v196 = QString::fromAscii_helper((QString *)"Slot_ReadClient", (const char *)0xFFFFFFFFLL, 32);
        v195 = (volatile signed __int32 *)QString::fromAscii_helper(
                                            (QString *)"[%1--%2] %3 cmd failed...failed...",
                                            (const char *)0xFFFFFFFFLL,
                                            v111);
        QString::arg((QString *)v197, (const QString *)&v195, (int)v196, 0);
        QString::arg((QString *)v198, (__int64)v197, 167, 0, (const QChar *)0xA);
        v193 = (volatile signed __int32 *)QString::fromAscii_helper(
                                            (QString *)"yyyy.MM.dd hh:mm:ss.zzz ",
                                            (const char *)0xFFFFFFFFLL,
                                            v112);
        QDateTime::currentDateTime((QDateTime *)v192);
        QDateTime::toString((QDateTime *)v194, (const QString *)v192);
        v113 = QTextStream::operator<<(v8, v194);
        v114 = (QTextStream *)QTextStream::operator<<(v113, v198);
        endl(v114);
        if ( !_InterlockedDecrement(v194[0]) )
          QString::free(v194[0]);
        v44 = (int *)v192;
        QDateTime::~QDateTime((QDateTime *)v192);
        if ( !_InterlockedDecrement(v193) )
        {
          v44 = (int *)v193;
          QString::free(v193);
        }
        if ( !_InterlockedDecrement(v198[0]) )
        {
          v44 = (int *)v198[0];
          QString::free(v198[0]);
        }
        if ( !_InterlockedDecrement((volatile signed __int32 *)v197[0]) )
        {
          v44 = (int *)v197[0];
          QString::free(v197[0]);
        }
        if ( !_InterlockedDecrement(v195) )
        {
          v44 = (int *)v195;
          QString::free(v195);
        }
        if ( !_InterlockedDecrement(*(volatile signed __int32 **)v196) )
        {
          v44 = *(int **)v196;
          QString::free(*(_QWORD *)v196);
        }
      }
      if ( !_InterlockedDecrement(*(volatile signed __int32 **)v218) )
      {
        v44 = v218;
        QList<QString>::free(v218, *(_QWORD *)v218);
      }
      if ( !_InterlockedDecrement(v217[0]) )
      {
        v44 = (int *)v217;
        QList<QString>::free(v217, v217[0]);
      }
      if ( !_InterlockedDecrement(v215[0]) )
      {
        v44 = (int *)v215[0];
        QString::free(v215[0]);
      }
    }
    else
    {
      v200 = &QByteArray::shared_null;
      _InterlockedIncrement(&QByteArray::shared_null);
      v199 = (volatile signed __int32 *)QString::fromAscii_helper(
                                          (QString *)"check md5 failed.",
                                          (const char *)0xFFFFFFFFLL,
                                          v38);
      v39 = QtServiceBase::instance((QtServiceBase *)"check md5 failed.");
      QtServiceBase::logMessage(v39, (__int64)&v199, 0);
      if ( !_InterlockedDecrement(v199) )
        QString::free(v199);
      v41 = _InterlockedDecrement((volatile signed __int32 *)v200);
      LOBYTE(v40) = v41 != 0;
      if ( !v41 )
        qFree(v200);
      v202 = (volatile signed __int32 *)QString::fromAscii_helper(
                                          (QString *)"yyyy.MM.dd hh:mm:ss.zzz ",
                                          (const char *)0xFFFFFFFFLL,
                                          v40);
      QDateTime::currentDateTime((QDateTime *)v201);
      QDateTime::toString((QDateTime *)v203, (const QString *)v201);
      v42 = QTextStream::operator<<(v8, v203);
      v43 = (QTextStream *)QTextStream::operator<<(v42, "check md5 failed.");
      endl(v43);
      if ( !_InterlockedDecrement(v203[0]) )
        QString::free(v203[0]);
      v44 = (int *)v201;
      QDateTime::~QDateTime((QDateTime *)v201);
      if ( !_InterlockedDecrement(v202) )
      {
        v44 = (int *)v202;
        QString::free(v202);
      }
    }
    v204 = (QtServiceBase *)&QByteArray::shared_null;
    _InterlockedIncrement(&QByteArray::shared_null);
    v45 = (QtServiceBase *)QtServiceBase::instance((QtServiceBase *)v44);
    QtServiceBase::logMessage((__int64)v45, (__int64)v146, 0);
    if ( !_InterlockedDecrement((volatile signed __int32 *)v204) )
    {
      v45 = v204;
      qFree(v204);
    }
    v205 = &QByteArray::shared_null;
    _InterlockedIncrement(&QByteArray::shared_null);
    v46 = QtServiceBase::instance(v45);
    QtServiceBase::logMessage(v46, (__int64)v15, 0);
    if ( !_InterlockedDecrement((volatile signed __int32 *)v205) )
      qFree(v205);
    (*(void (__fastcall **)(QAbstractSocket *))(*(_QWORD *)v119 + 112LL))(v119);
    if ( !(unsigned int)QAbstractSocket::state(v119) )
    {
      (*(void (__fastcall **)(QAbstractSocket *))(*(_QWORD *)v119 + 32LL))(v119);
      v207 = &QByteArray::shared_null;
      _InterlockedIncrement(&QByteArray::shared_null);
      v206 = (volatile signed __int32 *)QString::fromAscii_helper(
                                          (QString *)"UnconnectedState. delete socket.",
                                          (const char *)0xFFFFFFFFLL,
                                          v47);
      v48 = QtServiceBase::instance((QtServiceBase *)"UnconnectedState. delete socket.");
      QtServiceBase::logMessage(v48, (__int64)&v206, 0);
      if ( !_InterlockedDecrement(v206) )
        QString::free(v206);
      v50 = _InterlockedDecrement((volatile signed __int32 *)v207);
      LOBYTE(v49) = v50 != 0;
      if ( !v50 )
        qFree(v207);
      v209 = (volatile signed __int32 *)QString::fromAscii_helper(
                                          (QString *)"yyyy.MM.dd hh:mm:ss.zzz ",
                                          (const char *)0xFFFFFFFFLL,
                                          v49);
      QDateTime::currentDateTime((QDateTime *)v208);
      QDateTime::toString((QDateTime *)v210, (const QString *)v208);
      v51 = QTextStream::operator<<(v8, v210);
      v52 = (QTextStream *)QTextStream::operator<<(v51, "UnconnectedState. delete socket.");
      endl(v52);
      if ( !_InterlockedDecrement(v210[0]) )
        QString::free(v210[0]);
      QDateTime::~QDateTime((QDateTime *)v208);
      if ( !_InterlockedDecrement(v209) )
        QString::free(v209);
    }
    if ( !_InterlockedDecrement(v152[0]) )
      QString::free(v152[0]);
    if ( !_InterlockedDecrement((volatile signed __int32 *)v148[0]) )
      qFree(v148[0]);
    if ( !_InterlockedDecrement(v147) )
      QString::free(v147);
    if ( !_InterlockedDecrement(v146[0]) )
      QString::free(v146[0]);
    if ( !_InterlockedDecrement(v145[0]) )
      QString::free(v145[0]);
    if ( !_InterlockedDecrement(v144) )
      QString::free(v144);
    if ( !_InterlockedDecrement(v143) )
      QString::free(v143);
    if ( !_InterlockedDecrement(v142) )
      QString::free(v142);
    if ( !_InterlockedDecrement(v141) )
      QString::free(v141);
    if ( !_InterlockedDecrement(v140) )
      QString::free(v140);
    if ( !_InterlockedDecrement(v139) )
      QString::free(v139);
    if ( _InterlockedDecrement(*(volatile signed __int32 **)v133) )
      goto LABEL_37;
  }
  else if ( _InterlockedDecrement(*(volatile signed __int32 **)v133) )
  {
    goto LABEL_37;
  }
  QList<QString>::free(v133, *(_QWORD *)v133);
LABEL_37:
  if ( !_InterlockedDecrement((volatile signed __int32 *)v126[0]) )
    qFree(v126[0]);
  return __readfsqword(0x28u) ^ v222;
}