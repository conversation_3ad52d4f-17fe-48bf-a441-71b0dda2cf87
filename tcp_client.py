#!/usr/bin/env python3
"""
TCP Client script to send data to a target on port 5000
"""

import socket
import sys

def send_data_to_port(host, port, data):
    """
    Send data to the specified host and port via TCP
    
    Args:
        host (str): Target hostname or IP address
        port (int): Target port number
        data (str): Data to send
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create a TCP socket
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        
        # Set a timeout for the connection
        client_socket.settimeout(10)
        
        print(f"Connecting to {host}:{port}...")
        
        # Connect to the target
        client_socket.connect((host, port))
        print(f"Connected to {host}:{port}")
        
        # Send the data
        client_socket.send(data.encode('utf-8'))
        print(f"Sent data: {data}")
        
        # Close the connection
        client_socket.close()
        print("Connection closed")
        
        return True
        
    except socket.error as e:
        print(f"Socket error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    # Default values
    target_host = "localhost"  # Change this to your target host
    target_port = 5000         # Default port 5000
    message = "Hello, TCP Server!"  # Default message
    
    # Check if command line arguments are provided
    if len(sys.argv) > 1:
        target_host = sys.argv[1]
    
    if len(sys.argv) > 2:
        try:
            target_port = int(sys.argv[2])
        except ValueError:
            print("Invalid port number. Using default port 5000.")
            target_port = 5000
    
    if len(sys.argv) > 3:
        message = sys.argv[3]
    
    # Send the data
    success = send_data_to_port(target_host, target_port, message)
    
    if success:
        print("Data sent successfully!")
    else:
        print("Failed to send data.")
        sys.exit(1)

if __name__ == "__main__":
    main()