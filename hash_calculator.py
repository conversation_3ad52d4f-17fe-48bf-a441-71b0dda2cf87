#!/usr/bin/env python3
import hashlib

def calculate_hash(parts):
    """
    根据分析的逻辑计算hash值
    参数: parts - 前6个部分的列表
    返回: 计算出的hash值（十六进制字符串）
    """
    # 1. 将前6个部分用分隔符连接
    separator = "?%?"
    combined_string = separator.join(parts)
    
    print(f"前6个部分组合: {combined_string}")
    
    # 2. 追加固定字符串 "suwellservice"
    combined_with_suffix = combined_string + "suwellservice"
    print(f"追加suwellservice后: {combined_with_suffix}")
    
    # 3. 转换为UTF-8字节
    data_bytes = combined_with_suffix.encode('utf-8')
    
    # 4. 进行第一次MD5计算
    hash_result = hashlib.md5(data_bytes).digest()
    print(f"第1次MD5: {hash_result.hex()}")
    
    # 5. 进行4次迭代MD5计算
    for i in range(4):
        # 将当前hash结果转换为ASCII（这里应该是hex字符串的ASCII）
        hash_ascii = hash_result.hex().encode('ascii')
        # 追加到原hash数据
        combined_data = hash_result + hash_ascii
        # 再次MD5计算
        hash_result = hashlib.md5(combined_data).digest()
        print(f"第{i+2}次MD5: {hash_result.hex()}")
    
    # 6. 返回最终的十六进制字符串
    final_hash = hash_result.hex()
    return final_hash

def main():
    print("Hash计算器 - 根据前6个部分计算第7部分")
    print("=" * 50)
    
    # 获取用户输入
    parts = []
    for i in range(6):
        part = input(f"请输入第{i+1}个部分: ")
        parts.append(part)
    
    print("\n计算过程:")
    print("-" * 30)
    
    # 计算hash
    result_hash = calculate_hash(parts)
    
    print(f"\n最终结果:")
    print(f"第7部分的hash值: {result_hash}")
    
    # 构造完整的数据字符串
    separator = "?%?"
    full_data = separator.join(parts + [result_hash])
    print(f"\n完整数据: {full_data}")

if __name__ == "__main__":
    main()
